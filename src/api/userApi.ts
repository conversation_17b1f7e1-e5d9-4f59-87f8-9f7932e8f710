import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { AppLocalStorage } from '../utils';
import { ACCESS_TOKEN_NAME } from '../utils/constants';
import type { User } from './authApi';

interface UpdateProfileRequest {
  name: string;
  surname: string;
  username: string;
  birthday: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordResponse {
  message: string;
}

// User API slice - manages user profile, settings, and account operations
export const userApi = createApi({
  reducerPath: 'userApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: (headers) => {
      // TODO: Real token will be added later
      const token = AppLocalStorage.getItem({ key: ACCESS_TOKEN_NAME });
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    updateProfile: builder.mutation<User, UpdateProfileRequest>({
      query: (data) => ({
        url: '/auth/profile',
        method: 'PUT',
        body: data,
      }),
    }),

    changePassword: builder.mutation<ChangePasswordResponse, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

export const { useUpdateProfileMutation, useChangePasswordMutation } = userApi;
