@use '../../../styles/media' as *;

.container {
  display: flex;
  position: relative;

  & .mainWrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0; // prevent content overflow

    @include desktop {
      padding-left: 280px;
    }

    @include laptop {
      padding-left: 280px;
    }
  }

  & .contentWrapper {
    display: flex;
    width: 100%;
    max-width: 100%;
  }

  & .mainContent {
    flex: 1;
    min-width: 0;
    padding-top: 24px;
    padding-bottom: 24px;
    background: var(--neutral-700);

    @include mobile {
      padding-top: 12px;
    }
  }

  & .yellowCircle {
    position: fixed;
    bottom: 0;
    right: 0;
    width: 360px;
    height: 360px;
    background: var(--Primary-500, rgba(213, 255, 0, 1));
    border-radius: 50%;
    backdrop-filter: blur(300px);
    opacity: 0.3;
    pointer-events: none;
    z-index: 1;
  }
}
