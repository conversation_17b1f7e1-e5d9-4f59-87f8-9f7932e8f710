@use '../../../styles/media' as *;

.personalInfo {
  h2 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;

    @include mobile {
      margin-bottom: 24px;
    }
  }

  .title {
    color: var(--white);
  }

  .saveButton {
    min-width: 80px;
  }

  .form {
    margin-bottom: 40px;

    @include mobile {
      margin-bottom: 32px;
    }
  }

  .formRow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
  }

  label {
    cursor: pointer;
    color: var(--neutral-300);
    font-size: var(--font-size-xs);

    &::before {
      display: none;
    }
  }

  .formItem {
    margin-bottom: 20px;
  }

  .passwordSection {
    border-top: 1px solid var(--neutral-700);
    padding-top: 32px;

    @include mobile {
      padding-top: 24px;
    }
  }

  .passwordHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .passwordTitle {
    color: var(--white);
  }

  .changePasswordButton {
    min-width: 140px;

    @include mobile {
      min-width: 120px;
      font-size: var(--font-size-xs);
    }
  }
}
