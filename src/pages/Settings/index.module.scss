@use '../../styles/media' as *;

.settingsPage {
  min-height: 100vh;
}

.container {
  padding: 0 25px;

  @include mobile {
    padding: 0 15px;
  }
}

.header {
  margin-bottom: 20px;
}

h3 {
  color: var(--white);
}

.content {
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
}

.settingsTabs {
  :global(.ant-tabs-nav) {
    margin: 0;
    padding: 0;
    border-bottom: none;

    &::before {
      border: 0;
    }
  }

  :global(.ant-tabs-nav-list) {
    border-bottom: none;
    gap: 9px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 15px;
  }

  :global(.ant-tabs-tab) {
    background-color: var(--neutral-500);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    padding: 0 16px;
    margin: 0;
    border-radius: 50px;
    border: none;
    transition: all 0.2s ease;
    height: auto;
    min-height: 32px;
  }

  :global(.ant-tabs-tab-active) {
    background-color: var(--primary-500);
    color: var(--black);
  }

  :global(.ant-tabs-tab-btn) {
    color: inherit;
    font-weight: inherit;
    font-size: inherit;
  }

  :global(.ant-tabs-content-holder) {
    background-color: var(--neutral-600);
    padding: 25px 15px;
    border-radius: 8px;
  }

  :global(.ant-tabs-tabpane) {
    background-color: var(--neutral-600);
  }

  :global(.ant-tabs-ink-bar) {
    display: none;
  }
}
